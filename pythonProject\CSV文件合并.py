import pandas as pd
import os

# 指定文件夹路径
folder_path = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\窦性心律诊断为过缓\数据存储"

# 创建一个空列表来存储所有的数据框
all_dataframes = []

# 遍历文件夹中的所有文件
for filename in os.listdir(folder_path):
    if filename.endswith('.csv'):
        # 构建完整的文件路径
        file_path = os.path.join(folder_path, filename)

        # 读取CSV文件
        df = pd.read_csv(file_path)

        # 添加一列来标识数据来源
        df['数据来源'] = filename

        # 将数据框添加到列表中
        all_dataframes.append(df)

# 合并所有数据框
merged_df = pd.concat(all_dataframes, ignore_index=True)

# 保存合并后的文件
output_path = os.path.join(folder_path, '合并结果.csv')
merged_df.to_csv(output_path, index=False, encoding='utf-8-sig')

print(f"合并完成！共处理了 {len(all_dataframes)} 个文件")
print(f"结果保存在：{output_path}")