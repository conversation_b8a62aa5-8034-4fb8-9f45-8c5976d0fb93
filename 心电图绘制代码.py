from random import random
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import AutoMinorLocator
import os
from matplotlib import rcParams
from numpy.random import choice
import pandas as pd
import json
# 导入滤波功能所需的依赖项
from scipy.signal import medfilt, butter, lfilter, filtfilt
from scipy.signal.windows import gaussian
from sklearn.metrics import mean_squared_error
import pywt

rcParams['font.family'] = 'SimHei'

# 滤波函数
def wavelet_filter(ecg_signal, sampling_rate, wavelet='sym5', level=3,
                   cutoff=0.5, median_kernel=3, threshold_scale=0.8):
    """
    增强型心电图滤波函数，结合小波去噪、中值滤波和基线漂移校正

    参数：
    ecg_signal : numpy.ndarray
        输入的心电图信号
    sampling_rate : float
        采样频率 (Hz)
    wavelet : str
        小波基函数，默认使用'sym5'
    level : int
        小波分解层数，默认为5
    cutoff : float
        高通滤波器截止频率 (Hz)，默认为0.5Hz
    median_kernel : int
        中值滤波核大小，默认为3
    threshold_scale : float
        小波阈值缩放因子，默认为0.8

    返回：
    filtered_ecg : numpy.ndarray
        滤波后的心电图信号
    """
    # 第一步：中值滤波去除突刺噪声
    median_filtered = medfilt(ecg_signal, kernel_size=median_kernel)

    # 第二步：小波去噪
    coeffs = pywt.wavedec(median_filtered, wavelet, level=level)

    # 改进的阈值计算方法
    sigma = np.median(np.abs(coeffs[-level])) / 0.6745
    universal_thresh = sigma * np.sqrt(2 * np.log(len(ecg_signal)))

    # 对各层细节系数进行阈值处理
    for i in range(1, len(coeffs)):
        coeffs[i] = pywt.threshold(coeffs[i], threshold_scale * universal_thresh)

    # 第三步：基线漂移校正
    # 结合高通滤波
    wav_filtered = pywt.waverec(coeffs, wavelet)

    # 设计高通滤波器
    nyq = 0.5 * sampling_rate
    normal_cutoff = cutoff / nyq
    b, a = butter(4, normal_cutoff, btype='highpass')

    # 应用零相位滤波
    baseline_corrected = filtfilt(b, a, wav_filtered)

    # 确保信号长度一致
    if len(baseline_corrected) != len(ecg_signal):
        baseline_corrected = baseline_corrected[:len(ecg_signal)]

    return baseline_corrected

def calculate_snr(original_signal, filtered_signal):
    """
    计算信噪比（SNR）

    参数：
    original_signal : numpy.ndarray
        原始心电图信号
    filtered_signal : numpy.ndarray
        滤波后的心电图信号

    返回：
    snr : float
        信噪比，单位为dB
    """
    noise = original_signal - filtered_signal
    signal_power = np.mean(original_signal ** 2)
    noise_power = np.mean(noise ** 2)
    snr = 10 * np.log10(signal_power / noise_power)
    return snr

def calculate_rmse(original_signal, filtered_signal):
    """
    计算均方根误差（RMSE）

    参数：
    original_signal : numpy.ndarray
        原始心电图信号
    filtered_signal : numpy.ndarray
        滤波后的心电图信号

    返回：
    rmse : float
        均方根误差
    """
    rmse = np.sqrt(mean_squared_error(original_signal, filtered_signal))
    return rmse

def calculate_correlation_coefficient(original_signal, filtered_signal):
    """
    计算相关系数（CC）

    参数：
    original_signal : numpy.ndarray
        原始心电图信号
    filtered_signal : numpy.ndarray
        滤波后的心电图信号

    返回：
    cc : float
        相关系数
    """
    cc = np.corrcoef(original_signal, filtered_signal)[0, 1]
    return cc

color_list = ['red',
                'blue',
                'green',
                'orange',
                'purple'
        ]

def _ax_plot(ax, x, y, secs=10, lwidth=0.5, amplitude_ecg=1.8, time_ticks=0.2, start_sec=0, mark_dict=None):
    # 增幅参数
    zf = 0.4  # 增幅 (10mm/mv) / 读纸速度(25mm/s)
    # 设定X和Y轴刻度
    x_data_tick = np.arange(start_sec, start_sec + secs + 1, time_ticks)
    y_data_tick = np.arange(-amplitude_ecg, amplitude_ecg, time_ticks)
    major_xticklabels_list = ax.set_xticks(x_data_tick)
    ax.set_yticks(y_data_tick)

    # 设置X轴描述
    xticks_label = []
    x_index = 0
    for major_xticklabel in major_xticklabels_list:
        if major_xticklabels_list.index(major_xticklabel) % 5 != 0:
            xticks_label.append('')
        else:
            xticks_label.append(x_index + start_sec)
            x_index += 1
    ticklabels_list = ax.set_xticklabels(xticks_label)
    
    # 设置Y轴描述均为空
    ticklabels = ['' for _ in range(len(y_data_tick))]
    ax.set_yticklabels(ticklabels)

    ax.minorticks_on()
    ax.xaxis.set_minor_locator(AutoMinorLocator(5))
    ax.yaxis.set_minor_locator(AutoMinorLocator(5))
    ax.set_ylim(-amplitude_ecg, amplitude_ecg)
    ax.set_xlim(start_sec, start_sec + secs)
    ax.grid(which='major', linestyle='-', linewidth='0.5', color='red')
    ax.grid(which='minor', linestyle='-', linewidth='0.5', color=(1, 0.7, 0.7))

    # 绘制心电图主线，同时未设置 label
    ax.plot(x, [temp * zf for temp in y], linewidth=lwidth, label='ECG')

    if mark_dict:
        for k, v in mark_dict.items():
            lable_name = k
            x_data = v['x']
            y_data = v['y']
            color = v['color']
            ax.scatter(x[x_data], [temp * zf for temp in y_data], color=color, marker='x', s=50, label=lable_name)
        ax.legend()  # 仅在存在标记数据时显示图例


lead_index = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6']


def plot(ecg, sample_rate=500, title='ECG', fig_width=15, fig_height=2, line_w=0.5, ecg_amp=1.8, timetick=0.2,
         start_sec=0, mark_dict=None):
    """Plot multi lead ECG chart.
    # Arguments
        analysis        : m x n ECG signal data_train, which m is number of leads and n is length of signal.
        sample_rate: Sample rate of the signal.
        title      : Title which will be shown on top off chart
        fig_width  : The width of the plot
        fig_height : The height of the plot
    """
    plt.figure(figsize=(fig_width, fig_height))
    plt.suptitle(title, x=0.01, y=1)
    plt.subplots_adjust(
        hspace=0.04,
        wspace=0.04,
        left=0,  # the left side of the subplots of the figure
        right=1,  # the right side of the subplots of the figure
        bottom=0.24,  # the bottom of the subplots of the figure
        top=0.96
    )
    seconds = int(len(ecg) / sample_rate)

    ax = plt.subplot(1, 1, 1)
    # plt.rcParams['lines.linewidth'] = 5
    step = 1.0 / sample_rate
    _ax_plot(ax, np.arange(start_sec, start_sec + len(ecg) * step, step), ecg, seconds, line_w, ecg_amp, timetick,
             start_sec=start_sec, mark_dict=mark_dict)


DEFAULT_PATH = ''
show_counter = 1


def show_svg(tmp_path=DEFAULT_PATH):
    """Plot multi lead ECG chart.
    # Arguments
        tmp_path: path for temporary saving the result svg file
    """
    global show_counter
    file_name = tmp_path + "show_tmp_file_{}.svg".format(show_counter)
    plt.savefig(file_name)
    os.system("open {}".format(file_name))
    show_counter += 1
    plt.close()


def show():
    plt.show()


def save_as_png(file_name, path=DEFAULT_PATH, dpi=100, layout='tight'):
    """Plot multi lead ECG chart.
    # Arguments
        file_name: file_name
        path     : path to save image, defaults to current folder
        dpi      : set dots per inch (dpi) for the saved image
        layout   : Set equal to "tight" to include ax labels on saved image
    """
    plt.ioff()
    full_path = os.path.join(path, file_name + '.png')
    plt.savefig(full_path, dpi=dpi, bbox_inches=layout)
    plt.close()


def save_as_svg(file_name, path=DEFAULT_PATH):
    """Plot multi lead ECG chart.
    # Arguments
        file_name: file_name
        path     : path to save image, defaults to current folder
    """
    plt.ioff()
    full_path = os.path.join(path, file_name + '.svg')
    plt.savefig(full_path)
    plt.close()


def save_as_jpg(file_name, path=DEFAULT_PATH):
    """Plot multi lead ECG chart.
    # Arguments
        file_name: file_name
        path     : path to save image, defaults to current folder
    """
    plt.ioff()
    full_path = os.path.join(path, file_name + '.jpg')
    plt.savefig(full_path)
    plt.close()


def output_image(ecg_data, sample_rate, file_name, title='', file_path='image/', mark_dict=None, 
                original_data=None, comparison_title=''):
    """
    输出图片，支持对比显示原始数据和滤波后的数据
    
    Parameters
    ----------
    ecg_data : list or numpy.ndarray
        ECG数据（如果提供了original_data，则这个是滤波后的数据）
    sample_rate : int
        采样率
    file_name : str
        输出文件名（不含扩展名）
    title : str
        图表标题
    file_path : str
        输出文件路径
    mark_dict : dict, optional
        标记数据字典
    original_data : list or numpy.ndarray, optional
        原始ECG数据，如果提供则会显示对比图
    comparison_title : str, optional
        对比图的标题
    """
    # 确保输出目录存在
    os.makedirs(file_path, exist_ok=True)
    
    # 计算宽度
    length = len(ecg_data)
    if int(length / sample_rate) > 80:
        fig_width = 160
    else:
        fig_width = int(length / sample_rate) * 2

    # 截取数据
    data = ecg_data[:int(fig_width * sample_rate / 2)]
    
    # 如果提供了原始数据，绘制对比图
    if original_data is not None:
        # 创建两个子图
        plt.figure(figsize=(fig_width, 20))  # 增加高度以容纳两个图
        
        # 上图：原始数据
        plt.subplot(2, 1, 1)
        orig_data = original_data[:int(fig_width * sample_rate / 2)]
        plot(orig_data, sample_rate=sample_rate, title=comparison_title or '原始信号',
             fig_width=fig_width, fig_height=10,
             line_w=1.5, ecg_amp=1.8, timetick=0.2, start_sec=0, mark_dict=mark_dict)
        
        # 下图：滤波后数据
        plt.subplot(2, 1, 2)
        plot(data, sample_rate=sample_rate, title=title,
             fig_width=fig_width, fig_height=10,
             line_w=1.5, ecg_amp=1.8, timetick=0.2, start_sec=0, mark_dict=mark_dict)
        
        plt.tight_layout(pad=3.0)  # 增加子图之间的间距
        save_as_jpg(file_name, file_path)  # 保存图片
        plt.close()
    else:
        # 单图模式
        plot(data, sample_rate=sample_rate, title=title,
             fig_width=fig_width, fig_height=10,
             line_w=1.5, ecg_amp=1.8, timetick=0.2, start_sec=0, mark_dict=mark_dict)
        save_as_jpg(file_name, file_path)  # 保存图片


def random_color(exclude_colors=None):
    # 创建颜色列表的副本
    available_colors = color_list.copy()

    # 遍历要排除的颜色，并从副本列表中移除它们
    if exclude_colors:
        for color in exclude_colors:
            if color in available_colors:
                available_colors.remove(color)

            # 如果还有剩余颜色，则随机选择一个
    if available_colors:
        return choice(available_colors)
    else:
        # 如果没有剩余颜色（即所有颜色都被排除了），可以返回一个错误或默认颜色
        return None  # 或者你可以返回一个默认颜色，如 'black'

def process_folder(folder_path, output_base_directory):
    """
    处理文件夹中的所有JSON文件

    Parameters
    ----------
    folder_path : str
        包含JSON文件的文件夹路径
    output_base_directory : str
        输出图像的基础目录
    """
    # 确保输出目录存在
    os.makedirs(output_base_directory, exist_ok=True)

    # 获取文件夹中的所有JSON文件
    json_files = []
    for file_name in os.listdir(folder_path):
        if file_name.lower().endswith('.json'):
            json_files.append(os.path.join(folder_path, file_name))

    if not json_files:
        print(f"在文件夹 {folder_path} 中没有找到JSON文件")
        return

    print(f"找到 {len(json_files)} 个JSON文件，开始处理...")

    # 处理每个JSON文件
    successful_count = 0
    failed_count = 0

    for i, file_path in enumerate(json_files, 1):
        print(f"\n{'='*60}")
        print(f"处理文件 {i}/{len(json_files)}: {os.path.basename(file_path)}")
        print(f"{'='*60}")

        try:
            # 为每个文件创建单独的输出目录
            file_base_name = os.path.splitext(os.path.basename(file_path))[0]
            file_output_directory = os.path.join(output_base_directory, file_base_name)
            os.makedirs(file_output_directory, exist_ok=True)

            # 处理单个文件
            process_single_file(file_path, file_output_directory)
            successful_count += 1
            print(f"✓ 文件 {os.path.basename(file_path)} 处理成功")

        except Exception as e:
            failed_count += 1
            print(f"✗ 文件 {os.path.basename(file_path)} 处理失败: {str(e)}")
            continue

    print(f"\n{'='*60}")
    print(f"批量处理完成！")
    print(f"成功处理: {successful_count} 个文件")
    print(f"处理失败: {failed_count} 个文件")
    print(f"图像保存在: {output_base_directory}")
    print(f"{'='*60}")


def process_single_file(file_path, output_directory):
    """
    处理单个JSON文件

    Parameters
    ----------
    file_path : str
        JSON文件路径
    output_directory : str
        输出目录
    """
    DEFAULT_SAMPLE_RATE = 250
    time_length_seconds = 60

    # 滤波控制参数
    apply_filter = True  # 是否应用滤波
    wavelet_type = 'sym5'  # 小波类型
    wavelet_level = 3  # 小波分解级别
    cutoff_freq = 0.5  # 高通滤波器截止频率 (Hz)
    median_kernel_size = 3  # 中值滤波核大小
    threshold_scale = 0.8  # 小波阈值缩放因子
    calculate_metrics = True  # 是否计算滤波质量指标
    show_comparison = True  # 是否显示原始数据和滤波后的数据对比

    try:
        # 获取文件扩展名
        file_extension = os.path.splitext(file_path)[1].lower()

        # 根据文件扩展名选择不同的处理方式
        if file_extension == '.csv':
            print(f"检测到CSV格式文件: {file_path}")
            # 读取CSV文件的第一行数据
            df = pd.read_csv(file_path, header=None, nrows=1)
            if df.empty:
                print(f"错误：CSV文件为空。路径: {file_path}")
                raise ValueError("CSV文件为空")

            # 获取第一行的数据（所有数据点）
            parsed_ecg_data_list = [df.values[0].tolist()]

            # 获取文件名作为record_id
            base_filename = os.path.splitext(os.path.basename(file_path))[0]
            record_ids = [base_filename]

            print(f"从CSV文件中读取了 {len(parsed_ecg_data_list[0])} 个数据点")
            sample_rates = [DEFAULT_SAMPLE_RATE]
            report_statuses = [""]

        elif file_extension == '.json':
            print(f"检测到JSON格式文件: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data_records = json.load(f)

            # 兼容单条对象或对象列表
            if isinstance(data_records, dict):
                data_records = [data_records]
            elif not isinstance(data_records, list):
                raise ValueError(f"JSON文件内容不是预期的对象或列表格式: {file_path}")

            print(f"数据记录数量: {len(data_records)}")
            
            # 预处理所有记录
            parsed_ecg_data_list = []
            record_ids = []
            sample_rates = []
            report_statuses = []
            
            # 检查第一条记录，判断可能的数据结构
            if len(data_records) > 0:
                first_record = data_records[0]
                # 检查是否是原始数据结构（整个JSON就是一个ECG数据数组）
                if isinstance(first_record, (list, int, float)) or (isinstance(first_record, dict) and not any(key in first_record for key in ["ecg", "ecg_data", "dataId", "sampleRate"])):
                    print("检测到JSON文件是ECG数据数组格式，直接作为一条记录处理")
                    parsed_ecg_data_list = [data_records]
                    record_ids = ["ecg_data"]
                    sample_rates = [DEFAULT_SAMPLE_RATE]
                    report_statuses = [""]
                    
                    # 打印数据点数量
                    print(f"从JSON数组中读取了 {len(data_records)} 个数据点")
                else:
                    # 常规对象数组处理
                    print(f"按标准JSON格式处理 {len(data_records)} 条记录...")
                    for i, record in enumerate(data_records):
                        record_id = record.get("dataId", f"record_{i+1}")
                        # 尝试获取不同名称的ECG数据字段
                        ecg_raw = None
                        for field_name in ["ecg", "ecg_data", "ecgData", "data"]:
                            if field_name in record:
                                ecg_raw = record[field_name]
                                print(f"记录ID {record_id} 使用字段 '{field_name}' 获取数据")
                                break
                            
                        if ecg_raw is None:
                            print(f"警告：记录ID {record_id} 找不到ECG数据字段 (ecg/ecg_data/ecgData/data)，跳过。")
                            print(f"可用字段: {list(record.keys())}")
                            continue
                            
                        sample_rate = record.get("sampleRate", DEFAULT_SAMPLE_RATE)
                        report_status = record.get("reportStatus", "")
                        
                        parsed_ecg_data = []
                        if isinstance(ecg_raw, str) and ecg_raw.startswith('[') and ecg_raw.endswith(']'):
                            try:
                                parsed_ecg_data = json.loads(ecg_raw)
                            except Exception:
                                # 尝试用ast.literal_eval解析
                                import ast
                                try:
                                    parsed_ecg_data = ast.literal_eval(ecg_raw)
                                except Exception:
                                    print(f"警告：记录ID {record_id} 的ECG数据字段无法解析为数组，跳过。")
                                    continue
                        elif isinstance(ecg_raw, list):
                            parsed_ecg_data = ecg_raw
                        else:
                            print(f"警告：记录ID {record_id} 的ECG数据字段不是预期的字符串或数组格式，跳过。实际类型: {type(ecg_raw)}")
                            if isinstance(ecg_raw, dict):
                                print(f"  数据是字典类型，包含的键: {list(ecg_raw.keys())}")
                            continue

                        if not parsed_ecg_data:
                            print(f"警告：记录ID {record_id} 解析后ECG数据为空，跳过绘图。")
                            continue
                            
                        # 验证解析后的数据是否为数值列表
                        if not all(isinstance(x, (int, float)) for x in parsed_ecg_data[:100]):
                            print(f"警告：记录ID {record_id} 的数据不全是数值类型，可能格式有误。前几个值: {parsed_ecg_data[:5]}")
                            
                        # 数据长度检查
                        if len(parsed_ecg_data) < 100:  # 至少需要一些最小的数据量
                            print(f"警告：记录ID {record_id} 的ECG数据太短 ({len(parsed_ecg_data)} 个点)，可能不足以绘制有效图形。")
                        
                        parsed_ecg_data_list.append(parsed_ecg_data)
                        record_ids.append(record_id)
                        sample_rates.append(sample_rate)
                        report_statuses.append(report_status)
            
            if not parsed_ecg_data_list:
                raise ValueError("没有找到有效的ECG数据记录")

            print(f"成功解析 {len(parsed_ecg_data_list)} 条有效记录")

        else:
            raise ValueError(f"不支持的文件格式: {file_extension}。请提供CSV或JSON格式的文件")
            
        # 处理每个记录
        for i, (parsed_ecg_data, record_id, sample_rate, report_status) in enumerate(
                zip(parsed_ecg_data_list, record_ids, sample_rates, report_statuses)):
                
            # 截取前N秒的数据用于绘图
            time_length_points = sample_rate * time_length_seconds
            plot_data = parsed_ecg_data[:time_length_points]
            
            # 保存原始数据以用于对比
            original_data = np.array(plot_data) if show_comparison else None
            
            # 应用滤波
            if apply_filter:
                # 转换为numpy数组以确保兼容性
                plot_data_np = np.array(plot_data)
                filtered_data = wavelet_filter(
                    plot_data_np, 
                    sample_rate, 
                    wavelet=wavelet_type, 
                    level=wavelet_level,
                    cutoff=cutoff_freq, 
                    median_kernel=median_kernel_size, 
                    threshold_scale=threshold_scale
                )
                
                # 计算滤波质量指标
                if calculate_metrics:
                    snr = calculate_snr(plot_data_np, filtered_data)
                    rmse = calculate_rmse(plot_data_np, filtered_data)
                    cc = calculate_correlation_coefficient(plot_data_np, filtered_data)
                    print(f"记录ID {record_id} 滤波质量指标 - SNR: {snr:.2f}dB, RMSE: {rmse:.4f}, 相关系数: {cc:.4f}")
                
                # 使用滤波后的数据
                plot_data = filtered_data
                filter_status = "已滤波"
            else:
                filter_status = "原始信号"
                original_data = None  # 如果不应用滤波，则不需要对比

            # 获取唯一的文件名
            if file_extension == '.csv':
                base_filename = os.path.splitext(os.path.basename(file_path))[0]
                file_name = f"{base_filename}_{filter_status}"
            else:
                # 处理JSON格式时，对单个数据特殊处理
                if len(parsed_ecg_data_list) == 1 and record_id == "ecg_data":
                    # 这是从整个文件作为一个数组处理的情况
                    base_filename = os.path.splitext(os.path.basename(file_path))[0]
                    file_name = f"{base_filename}_{filter_status}"
                    # 使用文件名作为记录ID
                    record_id = base_filename
                else:
                    # 正常多条记录情况
                    file_name = f"{record_id}_{filter_status}"
                
            print(f"处理记录 {i+1}/{len(parsed_ecg_data_list)}: ID={record_id}, 数据点数={len(plot_data)}")
            
            # 如果显示对比，则传入原始数据
            title_status = f"状态: {report_status} " if report_status else ""
            output_image(
                plot_data, 
                sample_rate, 
                file_name,
                title=f"ECG Record ID: {record_id} {title_status}({filter_status})",
                file_path=output_directory,
                original_data=original_data,
                comparison_title=f"ECG Record ID: {record_id} {title_status}(原始信号)"
            )

        print(f"所有记录处理完毕。图像保存在: {output_directory}")

    except FileNotFoundError:
        raise FileNotFoundError(f"文件未找到: {file_path}")
    except json.JSONDecodeError:
        raise ValueError(f"解析JSON文件失败，请检查文件格式: {file_path}")
    except Exception as e:
        raise Exception(f"处理文件时发生错误: {str(e)}")


if __name__ == '__main__':
    # 处理整个文件夹中的JSON文件
    folder_path = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\窦性心律诊断为过缓"  # 包含JSON文件的文件夹路径
    output_base_directory = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\窦性心律诊断为过缓\批量图像输出"  # 输出图像的基础目录

    # 调用文件夹处理函数
    process_folder(folder_path, output_base_directory)